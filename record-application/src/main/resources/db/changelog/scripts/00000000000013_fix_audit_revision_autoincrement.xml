<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <changeSet id="fix_audit_log_revision_autoincrement" author="system">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="audit_log_revision" schemaName="record"/>
        </preConditions>
        <comment>Fix audit_log_revision_id to be auto-incrementing</comment>
        <sql splitStatements="false">
            <![CDATA[
                DO $$
                BEGIN
                    -- Drop the table if it exists and recreate it with proper auto-increment
                    IF EXISTS (SELECT 1 FROM information_schema.tables 
                              WHERE table_schema = 'record' 
                              AND table_name = 'audit_log_revision') THEN
                        DROP TABLE record.audit_log_revision;
                    END IF;
                    
                    -- Create the table with proper auto-increment
                    CREATE TABLE record.audit_log_revision (
                        audit_log_revision_id SERIAL PRIMARY KEY,
                        timestamp BIGINT NOT NULL,
                        auditor VARCHAR(255)
                    );
                END $$;
            ]]>
        </sql>
    </changeSet>
    
</databaseChangeLog>
