<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- Create schema first -->
    <include file="scripts/00000000000000_create_schema.xml" relativeToChangelogFile="true"/>

    <!-- Initial schema setup -->
    <include file="scripts/00000000000001_initial_schema.xml" relativeToChangelogFile="true"/>

    <!-- Document management tables -->
    <include file="scripts/00000000000002_document_tables.xml" relativeToChangelogFile="true"/>

    <!-- Code lookup table -->
    <include file="scripts/00000000000003_code_lookup_table.xml" relativeToChangelogFile="true"/>

    <!-- Entity fee table -->
    <include file="scripts/00000000000004_entity_fee_table.xml" relativeToChangelogFile="true"/>

    <!-- Entity group table -->
    <include file="scripts/00000000000005_entity_group_table.xml" relativeToChangelogFile="true"/>

    <!-- Entity note table -->
    <include file="scripts/00000000000006_entity_note_table.xml" relativeToChangelogFile="true"/>

    <!-- App properties table -->
    <include file="scripts/00000000000007_app_properties_table.xml" relativeToChangelogFile="true"/>

    <!-- Module definition table for module system -->
    <include file="scripts/00000000000008_module_definition.xml" relativeToChangelogFile="true"/>

    <!-- Audit library migration - rename columns and add audit fields -->
    <include file="scripts/00000000000009_audit_library_migration.xml" relativeToChangelogFile="true"/>

    <!-- Add missing audit columns and standardize column names -->
    <include file="scripts/00000000000010_add_missing_audit_columns.xml" relativeToChangelogFile="true"/>

    <!-- Property type table for audit library -->
    <include file="scripts/00000000000011_property_type.xml" relativeToChangelogFile="true"/>

</databaseChangeLog>